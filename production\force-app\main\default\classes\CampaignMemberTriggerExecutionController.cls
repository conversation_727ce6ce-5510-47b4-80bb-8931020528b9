/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 25/05/2021.
 */

public without sharing class CampaignMemberTriggerExecutionController implements TriggerHandler {

    private Set<Id> leadsMostRecentAction;
    private Set<Id> contactsMostRecentAction;
    private List<CampaignMember> campaignMembersNew;
    private Set<Id> campaignIds;

    //private Set<Id> campaignMembersForChatterGrouping;
    private Set<Id> campaignIdsToCallout;
    //private Set<Id> campaignIdsForChatterPost;
    //private Map<Id, CampaignMember> campaignMembersForChatterPost;
    //private List<CampaignMember> leadsForChatterPost;
    private List<CampaignMember> campaignMembersToCallout;
    private Map<Id, campaignMember> oldCampaignMembersMapToCallout;

    public Integer getRecursionDepth () {
        return 0;
    }

    public Integer getMaxRecursionDepthAllowed () {
        return 0;
    }

    public Boolean isEnabled () {
        return true;
    }

    private static List<Id> trialCampaignIds {
        get {
            if (trialCampaignIds == null) {
                trialCampaignIds = [SELECT Value__c FROM MSTR_Global_Configuation__mdt WHERE DeveloperName = 'Trial_Campaign_Ids'].Value__c.split(',');
            }
            return trialCampaignIds;
        }
        set;
    }

    public void bulkBefore () {
        campaignIds = new Set<Id>();
        campaignMembersNew = new List<CampaignMember>();
        campaignIdsToCallout = new Set<Id>();
        //campaignIdsForChatterPost = new Set<Id>();
        //campaignMembersForChatterPost = new Map<Id,CampaignMember>();
        //leadsForChatterPost = new List<CampaignMember>();
        campaignMembersToCallout = new List<CampaignMember>();
    }

    public void bulkAfter () {
        leadsMostRecentAction = new Set<Id>();
        contactsMostRecentAction = new Set<Id>();
        campaignMembersToCallout = new List<CampaignMember>();
        campaignIdsToCallout = new Set<Id>();
        oldCampaignMembersMapToCallout = new Map<Id, campaignMember>();
        //campaignMembersForChatterGrouping = new Set<Id>();
    }

    public void beforeInsert (SObject so) {
        CampaignMember campaignMember = (CampaignMember) so;
        Boolean isLead = campaignMember.LeadId != null && campaignMember.ContactId == null;
        Boolean isContact = campaignMember.ContactId != null && campaignMember.LeadId == null;
        campaignMembersToCallout.add(campaignMember);
        campaignIdsToCallout.add(campaignMember.CampaignId);

        if (campaignMember.CampaignId != null) {
            campaignIds.add(campaignMember.CampaignId);
            campaignMembersNew.add(campaignMember);
        }

        /*if(!campaignMember.Post_To_Chatter__c && (isLead || isContact)){
            campaignIdsForChatterPost.add(campaignMember.CampaignId);
            campaignMembersForChatterPost.put(campaignMember.Id, campaignMember);
            if(isLead){
                leadsForChatterPost.add(campaignMember);
            }
        }*/
    }

    public void beforeUpdate (SObject oldSo, SObject newSo) {
    }

    public void beforeDelete (SObject so) {
    }

    public void afterInsert (SObject so) {
        CampaignMember campaignMember = (CampaignMember) so;

        if (campaignMember.LeadId != null && campaignMember.CampaignId != TriggersHelper.redReportCampaignId && !campaignMember.Marketo_Excluded__c) {
            CampaignMemberLeadController.checkSyncToMarketo(campaignMember.LeadId);
            leadsMostRecentAction.add(campaignMember.LeadId);
        }

        if (campaignMember.ContactId != null && campaignMember.CampaignId != TriggersHelper.redReportCampaignId && !campaignMember.Marketo_Excluded__c) {
            CampaignMemberContactController.checkSyncToMarketo(campaignMember.ContactId);
            contactsMostRecentAction.add(campaignMember.ContactId);
        }

        /*if (campaignMember.Post_to_Chatter__c) {
            campaignMembersForChatterGrouping.add(campaignMember.Id);
        }*/
    }

    public void afterUpdate (SObject oldSo, SObject newSo) {
        CampaignMember oldCampaignMember = (CampaignMember) oldSo;
        CampaignMember newCampaignMember = (CampaignMember) newSo;
        If ((TriggersHelper.valueChangedNotBlank(oldCampaignMember, newCampaignMember, CampaignMember.TrialEndDate__c) && newCampaignMember.Email != null)
            || ((newCampaignMember.ContactId != null || newCampaignMember.LeadId != null) && newCampaignMember.TrialUpgradeRequest__c && TriggersHelper.valueChanged(oldCampaignMember, newCampaignMember, CampaignMember.TrialUpgradeRequest__c))){
                campaignIdsToCallout.add(newCampaignMember.CampaignId);
                campaignMembersToCallout.add(newCampaignMember);
                oldCampaignMembersMapToCallout.put(oldCampaignMember.Id,oldCampaignMember);
            }
        /*if (newCampaignMember.Post_to_Chatter__c) {
            if (!CampaignMemberTriggerHelper.VALID_STATUSES.contains(oldCampaignMember.Status) && CampaignMemberTriggerHelper.VALID_STATUSES.contains(newCampaignMember.Status) ||
                oldCampaignMember.Post_to_Chatter__c != newCampaignMember.Post_to_Chatter__c
            ) {
                campaignMembersForChatterGrouping.add(newCampaignMember.Id);

            }
        }*/
    }

    public void afterDelete (SObject so) {
        CampaignMember campaignMember = (CampaignMember) so;

        if (campaignMember.LeadId != null) {
            leadsMostRecentAction.add(campaignMember.LeadId);
        }

        if (campaignMember.ContactId != null) {
            contactsMostRecentAction.add(campaignMember.ContactId);
        }
    }

    public void afterUndelete (SObject so) {
    }

    public void andFinally () {
        List<Opportunity> opportunitiesToCreate = new List<Opportunity>();
        List<CampaignMemberFieldInputs> CampaignMemberFieldInputsToCallout = new List<CampaignMemberFieldInputs>();
        Map<Id,Contact> mapOfConIdVsContact = new Map<Id,Contact>();
        Map<Id,Lead> mapOfLeadIdVsLead = new Map<Id,Lead>();
        Set<Id> conIds = new Set<Id>();
        Set<Id> LeadIds = new Set<Id>();
        List<CampaignMember> campaignMembersToInsertOpportunities = new List<CampaignMember>();
        List<CampaignMember> campaignMembersToUpdateFields = new List<CampaignMember>();
        Campaign cmp;
        Map<Id,Campaign> queriedCampaigns = new Map<Id,Campaign>();
        Map<Id,Lead> queriedLeads = new Map<Id,Lead>();
        String campaignFields = '';
        String campaignFilter = '';
        String leadFields = '';
        String leadFilter = '';

        if (campaignIdsToCallout != null && !campaignIdsToCallout.isEmpty()) {
            campaignFields += 'Id,ParentId';
            campaignFilter += 'Id IN: campaignIdsToCallout';
        }
        /*if (campaignIdsForChatterPost != null && !campaignIdsForChatterPost.isEmpty()) {
            campaignFields += !String.isBlank(campaignFields)? ',': '';
            campaignFields += 'Post_To_Chatter__c ';
            campaignFilter += !String.isBlank(campaignFilter)? ' OR ': '';
            campaignFilter += 'Id IN: campaignIdsForChatterPost';
        }*/
        if(!String.isBlank(campaignFields) && !String.isBlank(campaignFilter)){
            queriedCampaigns = new Map<Id,Campaign>((List<Campaign>)Database.query('SELECT ' + campaignFields + ' ' +
                'FROM Campaign ' +
                'WHERE ' + campaignFilter));
        }

        if (campaignIdsToCallout != null && !campaignIdsToCallout.isEmpty()) {
            for (Id cmpId : campaignIdsToCallout) {
                cmp = queriedCampaigns.get(cmpId);
                if (cmp != null && (trialCampaignIds.contains(cmp.Id) || trialCampaignIds.contains(cmp.ParentId) || Test.isRunningTest())) {
                    for (CampaignMember cmpMember : campaignMembersToCallout) {
                        if (cmpMember.CampaignId == cmp.Id) {
                            if (oldCampaignMembersMapToCallout == null) {
                                campaignMembersToUpdateFields.add(cmpMember);
                                if ((cmpMember.ContactId != null || cmpMember.LeadId != null) && cmpMember.TrialUpgradeRequest__c) {
                                    if (cmpMember.ContactId != null)
                                        conIds.add(cmpMember.ContactId);
                                    else if (cmpMember.LeadId != null)
                                        LeadIds.add(cmpMember.LeadId);
                                    campaignMembersToInsertOpportunities.add(cmpMember);
                                }
                            }
                            else {
                                if (TriggersHelper.valueChangedNotBlank(oldCampaignMembersMapToCallout.get(cmpMember.Id), cmpMember, CampaignMember.TrialEndDate__c) && cmpMember.Email != null) {
                                    CampaignMemberFieldInputs fieldInputs = new CampaignMemberFieldInputs();
                                    fieldInputs.end_date = String.valueOf(cmpMember.TrialEndDate__c);
                                    fieldInputs.email = cmpMember.Email;
                                    CampaignMemberFieldInputsToCallout.add(fieldInputs);
                                }
                                if ((cmpMember.ContactId != null || cmpMember.LeadId != null) && cmpMember.TrialUpgradeRequest__c && TriggersHelper.valueChanged(oldCampaignMembersMapToCallout.get(cmpMember.Id), cmpMember, CampaignMember.TrialUpgradeRequest__c)){
                                    if (cmpMember.ContactId != null)
                                        conIds.add(cmpMember.ContactId);
                                    else if (cmpMember.LeadId != null)
                                        LeadIds.add(cmpMember.LeadId);
                                    campaignMembersToInsertOpportunities.add(cmpMember);
                                }
                            }
                        }
                    }
                }
            }
        }
        /*if (campaignIdsForChatterPost != null && !campaignIdsForChatterPost.isEmpty()) {
            for (Id cmpId : campaignIdsForChatterPost) {
                cmp = queriedCampaigns.get(cmpId);
                if (cmp != null && !cmp.Post_To_Chatter__c) {
                    campaignMembersForChatterPost.remove(cmpId);
                }
            }
        }*/
        if (!conIds.isEmpty()) {
            String contactQuery = 'SELECT Id, Account.Name, Account.OwnerId, Account.BillingCountry, Account.CurrencyIsoCode FROM Contact WHERE Id IN: conIds AND AccountId != null';
            mapOfConIdVsContact = new Map<Id,Contact>((List<Contact>)Database.query(contactQuery));
        }

        if (LeadIds != null && !LeadIds.isEmpty()) {
            leadFields += 'Id, Account_2__c, Account_2__r.Name, Account_2__r.OwnerId, Account_2__r.BillingCountry, Account_2__r.CurrencyIsoCode';
            leadFilter += '(Id IN: LeadIds';
        }
        /*if (leadsForChatterPost != null && !leadsForChatterPost.isEmpty()) {
            Set<Id> lIds = new Set<Id>();
            for(CampaignMember cmL: leadsForChatterPost){
                lIds.add(cmL.LeadId);
            }
            leadFields += !String.isBlank(leadFields)? '': 'Id';
            leadFilter += !String.isBlank(leadFilter)? ' OR ': '(';
            leadFilter += 'Id IN: lIds ';
        }*/
        leadFilter += ') ' + 'AND Account_2__c != null';
        if(!String.isBlank(leadFields) && !String.isBlank(leadFilter)){
            queriedLeads = new Map<Id,Lead>((List<Lead>)Database.query('SELECT ' + leadFields + ' ' +
                'FROM Lead ' +
                'WHERE ' + leadFilter));
        }
        if (LeadIds != null && !LeadIds.isEmpty()) {
            for(Id leadId: LeadIds){
                mapOfLeadIdVsLead.put(leadId, queriedLeads.get(leadId));
            }
        }
        /*if (leadsForChatterPost != null && !leadsForChatterPost.isEmpty()) {
            for(CampaignMember lChP: leadsForChatterPost){
                if(queriedLeads.get(lChP.LeadId) == null){
                    campaignMembersForChatterPost.remove(lChP.Id);
                }
            }
        }
        if(campaignMembersForChatterPost != null && !campaignMembersForChatterPost.isEmpty()){
            CampaignMemberTriggerHelper.setCampaignChatterPost(campaignMembersForChatterPost.values());
        }*/

        List<CampaignMember> mcsCampaignMembersToUpdate = new List<CampaignMember>();

        if (Trigger.isInsert) { // Process only for newly inserted CampaignMembers
            System.debug(LoggingLevel.INFO, 'MCS Processing: Starting MCS Campaign Member processing for insert trigger');
            System.debug(LoggingLevel.INFO, 'MCS Processing: Trigger.new size = ' + Trigger.new.size());

            String mcsCampaignId = null;
            MSTR_Global_Configuation__mdt mcsConfig = MSTR_Global_Configuation__mdt.getInstance(CampaignMemberTriggerHelper.MCS_CAMPAIGN_ID_METADATA_NAME);
            if (mcsConfig != null && String.isNotBlank(mcsConfig.Value__c)) {
                mcsCampaignId = mcsConfig.Value__c;
                System.debug(LoggingLevel.INFO, 'MCS Processing: Retrieved MCS Campaign ID = ' + mcsCampaignId);
            } else {
                System.debug(LoggingLevel.WARN, 'MCS Trial Campaign ID not configured in MSTR_Global_Configuation__mdt.');
            }

            String unmappedAccountId = null;
            MSTR_Global_Configuation__mdt mcsConfigForUnmappedAccount = MSTR_Global_Configuation__mdt.getInstance(CampaignMemberTriggerHelper.UNMAPPED_ACCCOUNT_ID_METADATA_NAME);
            if (mcsConfigForUnmappedAccount != null && String.isNotBlank(mcsConfigForUnmappedAccount.Value__c)) {
                unmappedAccountId = mcsConfigForUnmappedAccount.Value__c;
                System.debug(LoggingLevel.INFO, 'MCS Processing: Retrieved Unmapped Account ID = ' + unmappedAccountId);
            } else {
                System.debug(LoggingLevel.WARN, 'Unmapped Account ID not configured in MSTR_Global_Configuation__mdt.');
            }

            if (mcsCampaignId != null) {
                System.debug(LoggingLevel.INFO, 'MCS Processing: MCS Campaign ID is configured, proceeding with processing');

                List<Id> mcsCM_IdsToQuery = new List<Id>();
                Set<Id> mcs_LeadIdsForDetailsQuery = new Set<Id>();

                for (CampaignMember cmFromTrigger : (List<CampaignMember>)Trigger.new) {
                    System.debug(LoggingLevel.DEBUG, 'MCS Processing: Checking CampaignMember ID=' + cmFromTrigger.Id +
                                ', CampaignId=' + cmFromTrigger.CampaignId +
                                ', LeadId=' + cmFromTrigger.LeadId +
                                ', ContactId=' + cmFromTrigger.ContactId);

                    if (cmFromTrigger.CampaignId == mcsCampaignId) {
                        System.debug(LoggingLevel.INFO, 'MCS Processing: Found MCS Campaign Member ID=' + cmFromTrigger.Id);
                        mcsCM_IdsToQuery.add(cmFromTrigger.Id);

                        if (cmFromTrigger.LeadId != null && cmFromTrigger.ContactId == null) {
                            System.debug(LoggingLevel.INFO, 'MCS Processing: Adding Lead ID=' + cmFromTrigger.LeadId + ' for details query');
                            mcs_LeadIdsForDetailsQuery.add(cmFromTrigger.LeadId);
                        }
                    }
                }

                System.debug(LoggingLevel.INFO, 'MCS Processing: mcsCM_IdsToQuery count = ' + mcsCM_IdsToQuery.size());
                System.debug(LoggingLevel.INFO, 'MCS Processing: mcs_LeadIdsForDetailsQuery count = ' + mcs_LeadIdsForDetailsQuery.size());
                System.debug('mcs_LeadIdsForDetailsQuery -> ' + mcs_LeadIdsForDetailsQuery);

                if (!mcsCM_IdsToQuery.isEmpty()) {
                    System.debug(LoggingLevel.INFO, 'MCS Processing: Querying CampaignMembers for IDs: ' + mcsCM_IdsToQuery);

                    List<CampaignMember> mcsQueriedCampaignMembers = [
                        SELECT Id, CampaignId, LeadId, ContactId, Notes__c, TrialStartDate__c, TrialEndDate__c
                        FROM CampaignMember WHERE Id IN :mcsCM_IdsToQuery
                    ];

                    System.debug(LoggingLevel.INFO, 'MCS Processing: Retrieved ' + mcsQueriedCampaignMembers.size() + ' CampaignMembers from query');

                    Map<Id, Lead> mcsLeadDetailsMap = new Map<Id, Lead>();
                    if (!mcs_LeadIdsForDetailsQuery.isEmpty()) {
                        System.debug(LoggingLevel.INFO, 'MCS Processing: Querying Lead details for IDs: ' + mcs_LeadIdsForDetailsQuery);

                        mcsLeadDetailsMap = new Map<Id, Lead>([
                            SELECT Id, Company, Account_2__c
                            FROM Lead
                            WHERE Id IN :mcs_LeadIdsForDetailsQuery
                        ]);

                        System.debug(LoggingLevel.INFO, 'MCS Processing: Retrieved ' + mcsLeadDetailsMap.size() + ' Leads from query');
                        for (Id leadId : mcsLeadDetailsMap.keySet()) {
                            Lead lead = mcsLeadDetailsMap.get(leadId);
                            System.debug(LoggingLevel.DEBUG, 'MCS Processing: Lead ID=' + leadId +
                                        ', Company=' + lead.Company +
                                        ', Account_2__c=' + lead.Account_2__c);
                        }
                    } else {
                        System.debug(LoggingLevel.INFO, 'MCS Processing: No Lead IDs to query for details');
                    }

                    List<CampaignMember> mcsCMsReadyForProcessing = new List<CampaignMember>();
                    for(CampaignMember cm : mcsQueriedCampaignMembers){
                        System.debug(LoggingLevel.DEBUG, 'MCS Processing: Processing CampaignMember ID=' + cm.Id +
                                    ', LeadId=' + cm.LeadId +
                                    ', ContactId=' + cm.ContactId +
                                    ', Notes__c=' + cm.Notes__c +
                                    ', TrialStartDate__c=' + cm.TrialStartDate__c +
                                    ', TrialEndDate__c=' + cm.TrialEndDate__c);

                        if(cm.LeadId != null && cm.ContactId == null){ // Is a lead
                            System.debug(LoggingLevel.INFO, 'MCS Processing: Adding Lead CampaignMember ID=' + cm.Id + ' for processing');
                            mcsCMsReadyForProcessing.add(cm);
                        } else { // Is a contact or already converted lead
                            System.debug(LoggingLevel.INFO, 'MCS Processing: Adding Contact/Converted CampaignMember ID=' + cm.Id + ' for processing');
                            mcsCMsReadyForProcessing.add(cm);
                        }
                    }

                    System.debug(LoggingLevel.INFO, 'MCS Processing: mcsCMsReadyForProcessing count = ' + mcsCMsReadyForProcessing.size());

                    if (!mcsCMsReadyForProcessing.isEmpty()) {
                        System.debug(LoggingLevel.INFO, 'MCS Processing: Calling helper to set Trial Dates and Notes');
                        // Call helper to set Trial Dates and Notes
                        CampaignMemberTriggerHelper.processMCSCampaignMemberUpdates(mcsCMsReadyForProcessing, mcsLeadDetailsMap);

                        // Lead Conversion for MCS members
                        System.debug(LoggingLevel.INFO, 'MCS Processing: Starting Lead Conversion process');
                        List<Database.LeadConvert> leadsToConvert_MCS = new List<Database.LeadConvert>();

                        for (CampaignMember cm : mcsCMsReadyForProcessing) {
                            System.debug(LoggingLevel.DEBUG, 'MCS Processing: Checking conversion eligibility for CampaignMember ID=' + cm.Id +
                                        ', LeadId=' + cm.LeadId +
                                        ', ContactId=' + cm.ContactId);

                            if (cm.LeadId != null && cm.ContactId == null && mcsLeadDetailsMap.containsKey(cm.LeadId)) {
                                Lead leadDetail = mcsLeadDetailsMap.get(cm.LeadId);
                                System.debug(LoggingLevel.INFO, 'MCS Processing: Preparing Lead conversion for Lead ID=' + cm.LeadId +
                                            ', Company=' + leadDetail.Company +
                                            ', Account_2__c=' + leadDetail.Account_2__c);

                                Id acntID = null;
                                if(leadDetail.Account_2__c != null) {
                                    acntId = leadDetail.Account_2__c;
                                    System.debug(LoggingLevel.INFO, 'MCS Processing: Using existing Account ID=' + acntId + ' for Lead conversion');
                                } else {
                                    acntId = unmappedAccountId;
                                    System.debug(LoggingLevel.INFO, 'MCS Processing: Using unmapped Account ID=' + acntId + ' for Lead conversion');
                                }

                                Database.LeadConvert lc = new Database.LeadConvert();
                                lc.setLeadId(leadDetail.Id);
                                lc.setConvertedStatus('Converted');
                                lc.setDoNotCreateOpportunity(true); // IMPORTANT: Do not create an Opportunity
                                lc.setAccountId(acntId);
                                leadsToConvert_MCS.add(lc);

                                System.debug(LoggingLevel.INFO, 'MCS Processing: Added Lead ID=' + leadDetail.Id + ' to conversion list with Account ID=' + acntId);
                            } else {
                                if (cm.LeadId != null && cm.ContactId == null) {
                                    System.debug(LoggingLevel.WARN, 'MCS Processing: Lead ID=' + cm.LeadId + ' not found in mcsLeadDetailsMap, skipping conversion');
                                } else {
                                    System.debug(LoggingLevel.DEBUG, 'MCS Processing: CampaignMember ID=' + cm.Id + ' is not eligible for Lead conversion (already Contact or no Lead)');
                                }
                            }
                        }

                        System.debug(LoggingLevel.INFO, 'MCS Processing: Total Leads prepared for conversion: ' + leadsToConvert_MCS.size());

                        if (!leadsToConvert_MCS.isEmpty()) {
                            System.debug(LoggingLevel.INFO, 'MCS Processing: Executing Lead conversion for ' + leadsToConvert_MCS.size() + ' Leads');

                            List<Database.LeadConvertResult> lcResults_MCS = Database.convertLead(leadsToConvert_MCS, false);
                            System.debug(LoggingLevel.INFO, 'MCS Processing: Lead conversion completed, processing results');

                            for (Database.LeadConvertResult lcr : lcResults_MCS) {
                                if (lcr.isSuccess()) {
                                    System.debug(LoggingLevel.INFO, 'MCS Processing: Lead conversion successful for Lead ID=' + lcr.getLeadId() +
                                                ', new Contact ID=' + lcr.getContactId() +
                                                ', Account ID=' + lcr.getAccountId());

                                    // // Update the CampaignMember with the new ContactId
                                    // for (CampaignMember cm : mcsCMsReadyForProcessing) {
                                    //     if (cm.LeadId == lcr.getLeadId()) {
                                    //         System.debug(LoggingLevel.INFO, 'MCS Processing: Updating CampaignMember ID=' + cm.Id +
                                    //                     ' with new Contact ID=' + lcr.getContactId());
                                    //         cm.ContactId = lcr.getContactId();
                                    //         // cm.LeadId = null; // SF usually handles this post-conversion for the CM object in DB.
                                    //                           // The in-memory object might still have LeadId temporarily.
                                    //     }
                                    // }
                                } else {
                                    System.debug(LoggingLevel.ERROR, 'MCS Lead Conversion Error for Lead ID ' + lcr.getLeadId() + ': ' + lcr.getErrors()[0].getMessage());
                                    // Potentially log these errors more formally
                                }
                            }
                        } else {
                            System.debug(LoggingLevel.INFO, 'MCS Processing: No Leads to convert');
                        }

                        System.debug(LoggingLevel.INFO, 'MCS Processing: Adding ' + mcsCMsReadyForProcessing.size() + ' CampaignMembers to update list');
                        System.debug('mcsCMsReadyForProcessing -> ' + mcsCMsReadyForProcessing);
                        mcsCampaignMembersToUpdate.addAll(mcsCMsReadyForProcessing);
                    }
                }
            }
        }


        //Don't run default status rules for Marketo Integration user.
        if (UserInfo.getUserId() != TriggersHelper.marketoUserConfiguration.Value__c && campaignMembersNew != null && !campaignMembersNew.isEmpty()) {
            CampaignMemberTriggerHelper.stampCampaignMemberStatus(campaignMembersNew, campaignIds);
        }

        if (leadsMostRecentAction != null && !leadsMostRecentAction.isEmpty()) {
            CampaignMemberLeadController.stampCampaignFields(leadsMostRecentAction);
        }

        if (contactsMostRecentAction != null && !contactsMostRecentAction.isEmpty()) {
            CampaignMemberContactController.stampCampaignFields(contactsMostRecentAction);
        }

        /*if (campaignMembersForChatterGrouping != null && !campaignMembersForChatterGrouping.isEmpty()){
            CampaignMemberTriggerHelper.sendChatterNotification(campaignMembersForChatterGrouping);
        }*/

        CampaignMemberLeadController.updateLeads();
        CampaignMemberContactController.updateContacts();

        System.debug(LoggingLevel.INFO, 'MCS Processing: Preparing final update map with ' + mcsCampaignMembersToUpdate.size() + ' MCS CampaignMembers');

        Map<Id, CampaignMember> finalCampaignMembersToUpdateMap = new Map<Id, CampaignMember>();
        // Add MCS CMs (already processed with dates, notes, and ContactId from conversion)
        for(CampaignMember cm : mcsCampaignMembersToUpdate) {
            System.debug(LoggingLevel.DEBUG, 'MCS Processing: Adding CampaignMember ID=' + cm.Id + ' to final update map');
            finalCampaignMembersToUpdateMap.put(cm.Id, cm);
        }

        Set<Id> mcsProcessedCampaignMemberIdsForCallout = new Set<Id>();
        for (CampaignMember cm : mcsCampaignMembersToUpdate) {
            mcsProcessedCampaignMemberIdsForCallout.add(cm.Id);
        }

        System.debug('mcsProcessedCampaignMemberIdsForCallout -> ' + mcsProcessedCampaignMemberIdsForCallout);
        if (!mcsProcessedCampaignMemberIdsForCallout.isEmpty()) {
            System.debug(LoggingLevel.INFO, 'MCS Processing: Queuing MCS Trial Provisioning for CampaignMember IDs: ' + mcsProcessedCampaignMemberIdsForCallout);
            if(!Test.isRunningTest()){
                CampaignMemberTriggerHelper.provisionMCSTrialEnvironments(mcsProcessedCampaignMemberIdsForCallout);
            }
        }

        System.debug(LoggingLevel.INFO, 'MCS Processing: Final update map contains ' + finalCampaignMembersToUpdateMap.size() + ' CampaignMembers');

        if (!finalCampaignMembersToUpdateMap.isEmpty()) {
            System.debug(LoggingLevel.INFO, 'MCS Processing: Executing update for ' + finalCampaignMembersToUpdateMap.size() + ' CampaignMembers');
            update finalCampaignMembersToUpdateMap.values();
            System.debug(LoggingLevel.INFO, 'MCS Processing: CampaignMember update completed successfully');
        } else {
            System.debug(LoggingLevel.INFO, 'MCS Processing: No CampaignMembers to update');
        }

        //update CampaignMember's Fields
        if (!campaignMembersToUpdateFields.isEmpty()) {
            CampaignMemberTriggerHelper.UpdateCampaignMemberFields(campaignMembersToUpdateFields);
        }
        //insert Opportunities
        if (!campaignMembersToInsertOpportunities.isEmpty()) {
            CampaignMemberTriggerHelper.insertOpportunities(campaignMembersToInsertOpportunities, mapOfConIdVsContact, mapOfLeadIdVsLead, opportunitiesToCreate);
        }
        //update push CampaignMember's fields through callout
        if (!CampaignMemberFieldInputsToCallout.isEmpty()) {
            CampaignMemberTriggerHelper.calloutToPushCampaignMemberFieldsUpdate(JSON.serialize(CampaignMemberFieldInputsToCallout));
        }
        if (!opportunitiesToCreate.isEmpty())
            insert opportunitiesToCreate;
    }

    private class CampaignMemberFieldInputs {
        private String end_Date;
        private String email;
    }
}