/************************************* MODIFICATION LOG ********************************************************************************************
 * MCSDependencyBatch
 *
 * DESCRIPTION : Batch that runs every hour to create Entitlement, Opp, Quote in Bulk
 * Test Class: CreateMCSDSITest
 *
 *---------------------------------------------------------------------------------------------------------------------------------------------------
 * DEVELOPER                     DATE                                   REASON
 *---------------------------------------------------------------------------------------------------------------------------------------------------
 * Mahipal Jat		            14/05/2025                              - Original Version - 557760 - SF/CPQ - MCS - Auto Opp Creation
 */

 public class MCSDependencyBatch implements Database.Batchable<SObject>, Database.Stateful {
    public List<SBQQ__Quote__c> quotesToInsert = new List<SBQQ__Quote__c>();
    public String query = '';
    public String mcsConfigForUnmappedAccountID = MSTR_Global_Configuation__mdt.getInstance(CampaignMemberTriggerHelper.UNMAPPED_ACCCOUNT_ID_METADATA_NAME).Value__c;
    
     public MCSDependencyBatch(String query) {
        this.query = query;
    }
    public MCSDependencyBatch() {
        
        this.query = ' MCSBatchStatus__c = \'Pending\' AND MCSEnvironmentOwner__c != null AND MCSEnvironmentOwner__r.AccountId != :mcsConfigForUnmappedAccountID';
    }

    public Database.QueryLocator start(Database.BatchableContext BC) {
        DescribeSObjectResult describeResult = DSI__c.getSObjectType().getDescribe();
        List<String> fieldNames = new List<String>(describeResult.fields.getMap().keySet());
        String query = ' SELECT ' + String.join(fieldNames, ',') + ', (SELECT Id FROM Product_Keys__r)' + ' FROM ' + describeResult.getName() + ' WHERE ' + query;
        System.debug('query ' + query);

        return Database.getQueryLocator(query);
    }

    public void execute(Database.BatchableContext BC, List<DSI__c> scope) {
        Set<Id> MCSOwnerIDs = new Set<Id>();
        Map<Id, Contact> ownerContactMap = new Map<Id, Contact>();
        Map<Id, Entitlement> entitlementDSIMap = new Map<Id, Entitlement>();
        Map<Id, List<EntitlementContact>> entitlementContactMap = new Map<Id, List<EntitlementContact>>();
        Map<Id, List<Opportunity>> oppDSIMap = new Map<Id, List<Opportunity>>();
        MSTR_Placeholder_Values__c placeholder = MSTR_Placeholder_Values__c.getOrgDefaults();
        SlaProcess MCSSLA = [SELECT Id, Name, BusinessHoursId FROM SlaProcess WHERE Name = 'Cloud MCS' LIMIT 1];
        List<FF_Integration_Log__c> batchErrorsList = new List<FF_Integration_Log__c>();
        Map<Id, FF_Integration_Log__c> batchErrorDSIMap = new Map<Id, FF_Integration_Log__c>();

        List<MSTR_Global_Configuation__mdt> campaignIdList = [
            SELECT Value__c
            FROM MSTR_Global_Configuation__mdt
            WHERE DeveloperName = 'MCS_Trial_Campaign_Id'
            LIMIT 1
        ];

        List<String> microstrategyAccountsIds = [
                SELECT Value__c
                FROM MSTR_Global_Configuation__mdt
                WHERE DeveloperName = 'MicroStrategy_Accounts'
                LIMIT 1
            ]
            .Value__c.split(';');

        List<MSTR_Global_Configuation__mdt> defaultOwnerList = [
            SELECT value__c
            FROM MSTR_Global_Configuation__mdt
            WHERE developerName = 'MCS_Default_Owner'
        ];
        List<User> userDefault;
        if (!defaultOwnerList.isEmpty()) {
            MSTR_Global_Configuation__mdt defaultUser = defaultOwnerList[0];
            userDefault = [SELECT Id, FirstName, LastName, email FROM User WHERE id = :defaultUser.value__c];
        }

        for (DSI__c dsi : scope) {
            mcsOwnerIDs.add(dsi.MCSEnvironmentOwner__c);
        }

        //grab contact
        for (Contact cont : [
            SELECT
                Id,
                AccountId,
                Account.District__c,
                Account.District__r.Business_Hours__c,
                Account.Name,
                MailingCountry,
                Account.Owner.Id,
                Account.Owner.Department,
                Name
            FROM Contact
            WHERE Id IN :mcsOwnerIDs
        ]) {
            for (DSI__c dsi : scope) {
                if (dsi.MCSEnvironmentOwner__c == cont.Id) {
                    ownerContactMap.put(dsi.Id, cont);
                }
            }
        }

        //grab existing Entitlements for DSIs
        for (Entitlement ent : [SELECT Id, DSI__c, AccountId, StartDate, EndDate FROM Entitlement WHERE DSI__c IN :scope]) {
            if (!entitlementDSIMap.containsKey(ent.DSI__c)) {
                entitlementDSIMap.put(ent.DSI__c, ent);
            }
        }

        //grab existing Entitlement Contacts for DSIs
        for (EntitlementContact entContact : [
            SELECT Id, ContactId, EntitlementId, Entitlement.DSI__c
            FROM EntitlementContact
            WHERE Entitlement.DSI__c IN :scope
        ]) {
            if (entitlementContactMap.containsKey(entContact.Entitlement.DSI__c)) {
                List<EntitlementContact> entContactList = entitlementContactMap.get(entContact.Entitlement.DSI__c);
                entContactList.add(entContact);
            } else {
                List<EntitlementContact> entContactList = new List<EntitlementContact>();
                entContactList.add(entContact);
                entitlementContactMap.put(entContact.Entitlement.DSI__c, entContactList);
            }
        }

        //grab existing Opportunities for DSIs
        for (Opportunity opp : [SELECT Id, DSI__c FROM Opportunity WHERE DSI__c IN :scope]) {
            if (oppDSIMap.containsKey(opp.DSI__c)) {
                List<Opportunity> oppList = oppDSIMap.get(opp.DSI__c);
                oppList.add(opp);
            } else {
                List<Opportunity> oppList = new List<Opportunity>();
                oppList.add(opp);
                oppDSIMap.put(opp.DSI__c, oppList);
            }
        }

        List<Entitlement> entitlementList = new List<Entitlement>();

        for (DSI__c dsi : scope) {
            //create Entitlement if it does not exist
            Contact relatedContact = ownerContactMap.get(dsi.Id);
            System.debug('dsi -> ' + dsi);
            System.debug('relatedContact -> ' + relatedContact);

            if (relatedContact != null) {
                //only perform if there dsi has bene assigned with an owner
                if (!entitlementDSIMap.containsKey(dsi.Id)) {
                    Entitlement ent = new Entitlement();
                    ent.DSI__c = dsi.Id;
                    ent.AccountId = relatedContact.AccountId;
                    ent.Support_Level__c = 'Cloud MCS';
                    ent.Name = dsi.Name + ' - Cloud MCS';
                    ent.SlaProcessId = mcsSLA.Id;
                    ent.BusinessHoursId = relatedContact.Account.District__r.Business_Hours__c != null
                        ? relatedContact.Account.District__r.Business_Hours__c
                        : placeholder.Default_Business_Hours__c;
                    ent.LastUpdateddate__c = System.now();
                    ent.Type = 'Email Support';
                    if (dsi.MCSActivationDateTime__c != null) {
                        ent.StartDate = Date.valueOf(dsi.MCSActivationDateTime__c.format('yyyy-MM-dd'));
                        ent.EndDate = Date.valueOf(dsi.MCSExpirationDateTime__c.format('yyyy-MM-dd'));
                    } else {
                        //default to today + 90 days
                        ent.StartDate = System.today();
                        ent.EndDate = System.today().addDays(90);
                    }
                    entitlementList.add(ent);
                    entitlementDSIMap.put(dsi.Id, ent);
                } else {
                    Entitlement ent = entitlementDSIMap.get(dsi.Id);
                    if (
                        ent.AccountId != relatedContact.AccountId ||
                        (dsi.MCSActivationDateTime__c != null &&
                        ent.StartDate != Date.valueOf(dsi.MCSActivationDateTime__c.format('yyyy-MM-dd'))) ||
                        (dsi.MCSExpirationDateTime__c != null &&
                        ent.EndDate != Date.valueOf(dsi.MCSExpirationDateTime__c.format('yyyy-MM-dd')))
                    ) {
                        ent.AccountId = relatedContact.AccountId;
                        ent.StartDate = Date.valueOf(dsi.MCSActivationDateTime__c.format('yyyy-MM-dd'));
                        ent.EndDate = Date.valueOf(dsi.MCSExpirationDateTime__c.format('yyyy-MM-dd'));
                        entitlementList.add(ent);
                    }
                }
            }
        }

        if (!entitlementList.isEmpty()) {
            Database.UpsertResult[] upsertResult = Database.upsert(entitlementList, false);

            for (Integer i = 0; i < entitlementList.size(); i++) {
                if (!upsertResult[i].isSuccess()) {
                    updateErrorMap(
                        entitlementList[i].DSI__c,
                        batchErrorDSIMap,
                        'Entitlement Failed - ',
                        upsertResult[i].getErrors()
                    );
                    for (DSI__c dsi : scope) {
                        if (dsi.Id == entitlementList[i].DSI__c) {
                            dsi.MCSBatchStatus__c = 'Error';
                            break;
                        }
                    }
                }
            }
        }

        List<EntitlementContact> newEntContactList = new List<EntitlementContact>();
        for (DSI__c dsi : scope) {
            Contact relatedContact = ownerContactMap.get(dsi.Id);
            if (relatedContact != null) {
                //create EntitlementContact
                if (entitlementContactMap.containsKey(dsi.Id)) {
                    List<EntitlementContact> entContactList = entitlementContactMap.get(dsi.Id);
                    Boolean hasContact = false;
                    for (EntitlementContact entContact : entContactList) {
                        if (entContact.ContactId == relatedContact.Id) {
                            //dsi owner exists
                            hasContact = true;
                            break;
                        }
                    }
                    if (!hasContact) {
                        //existing list does not contain contact so create a new one
                        Entitlement ent = entitlementDSIMap.get(dsi.Id);
                        EntitlementContact entContact = new EntitlementContact(
                            ContactId = relatedContact.Id,
                            EntitlementId = ent.Id
                        );
                        newEntContactList.add(entContact);
                    }
                } else {
                    //create EntitlementContact because no existing entitlementContacts
                    Entitlement ent = entitlementDSIMap.get(dsi.Id);
                    EntitlementContact entContact = new EntitlementContact(ContactId = relatedContact.Id, EntitlementId = ent.Id);
                    newEntContactList.add(entContact);
                }
            }
        }
        System.debug('newEntContactList -> ' + newEntContactList);
        System.debug('newEntContactList.size -> ' + newEntContactList.size());

        if (!newEntContactList.isEmpty()) {
            Database.SaveResult[] saveResult = Database.insert(newEntContactList, false);
        }

        List<Opportunity> newOppList = new List<Opportunity>();

        for (DSI__c dsi : scope) {
            Contact c = ownerContactMap.get(dsi.Id);
            System.debug('c.AccountId -> ' + c.AccountId);
            System.debug('microstrategyAccountsIds -> ' + microstrategyAccountsIds);
            Boolean isInternal = microstrategyAccountsIds.contains(c.AccountId);
            System.debug('isInternal -> ' + isInternal);

            if (c != null && !isInternal) {
                //create Opp only if one does not exist
                if (!oppDSIMap.containsKey(dsi.Id)) {
                    Id newSaleRecTypeId = Schema.SObjectType.Opportunity.getRecordTypeInfosByName()
                        .get('Sales Opportunity (New)')
                        .getRecordTypeId();
                    //populate map
                    Map<String, Country_Map__mdt> CountryNameMap = new Map<String, Country_Map__mdt>();
                    for (Country_Map__mdt metaRec : [SELECT Id, MasterLabel, Region__c FROM Country_Map__mdt]) {
                        CountryNameMap.put(metaRec.MasterLabel, metaRec);
                    }
                    Entitlement ent = entitlementDSIMap.get(dsi.Id);

                    Opportunity newOpp = new Opportunity();
                    newOpp.AccountId = c.AccountId;
                    newOpp.District__c = c.Account.District__c;
                    newOpp.StageName = 'S1 - Recognize Needs';
                    newOpp.CloseDate = ent.endDate;
                    newOpp.Due_Date__c = ent.endDate;
                    newOpp.Business_Executive__c = c.Id;
                    newOpp.SBCF_Type_of_Sale__c = 'Direct Sale';
                    newOpp.Name =  c.Account.Name + ' - Managed Cloud Standard - '+ dsi.mcsEnvironmentName__c;
                    newOpp.CPQ_UAT_Name__c = 'Managed Cloud Standard';
                    newOpp.DSI__c = dsi.Id;
                    if (c.Account.Owner.Department == 'SLS') {
                        newOpp.OwnerId = c.Account.OwnerId;
                    } else if (userDefault != null) {
                        newOpp.OwnerId = userDefault[0].Id;
                    }
                    newOpp.RecordTypeId = newSaleRecTypeId;
                    if (!campaignIdList.isEmpty() && !Test.isRunningTest()) {
                        newOpp.CampaignId = campaignIdList[0].Value__c;
                    }
                    newOpp.Internal_Comments_Long__c = '';

                    //populate pricebook region if it exists or default to United States USD
                    Country_Map__mdt countryMapRecord = CountryNameMap.get(c.MailingCountry);
                    if (countryMapRecord != null && countryMapRecord.Region__c != null) {
                        newOpp.RegionPricebook__c = countryMapRecord.Region__c;
                    } else {
                        System.debug('default value for region pricebook');
                        newOpp.RegionPricebook__c = 'United States - USD';
                    }
                    newOppList.add(newOpp);
                }
            }
        }

        if (!newOppList.isEmpty()) {
            Database.SaveResult[] saveResult = Database.insert(newOppList, false);
            for (Integer i = 0; i < newOppList.size(); i++) {
                if (!saveResult[i].isSuccess()) {
                    updateErrorMap(
                        newOppList[i].DSI__c,
                        batchErrorDSIMap,
                        'Opportunity Insert Failed - ',
                        saveResult[i].getErrors()
                    );
                    for (DSI__c dsi : scope) {
                        if (dsi.Id == newOppList[i].DSI__c) {
                            dsi.MCSBatchStatus__c = 'Error';
                            break;
                        }
                    }
                } else {
                    Opportunity newOpp = newOppList.get(i);

                    //create Quote
                    Id draftRecordTypeId = Schema.SObjectType.SBQQ__Quote__c.getRecordTypeInfosByName()
                        .get('Draft Quote')
                        .getRecordTypeId();
                    SBQQ__Quote__c quote = new SBQQ__Quote__c();
                    quote.SBQQ__Account__c = newOpp.AccountId;
                    quote.SBQQ__Opportunity2__c = newOpp.Id;
                    quote.SBQQ__StartDate__c = newOpp.CloseDate;
                    quote.SBQQ__Primary__c = false;
                    //reusing this field of MCI for MSI 
                    quote.Is_MCI_DCI__c = true;
                    quote.SBQQ__SubscriptionTerm__c = 12;
                    quote.RecordTypeId = draftRecordTypeId;
                    quote.AccountOwner__c = newOpp.OwnerId;
                    quote.OwnerId = newOpp.OwnerId;
                    quote.CurrencyIsoCode = newOpp.CurrencyIsoCode;
                    quote.SBQQ__Primary__c = true;
                    quotesToInsert.add(quote);
                }
            }
        }

        //insert integration failures
        batchErrorsList = batchErrorDSIMap.values();
        if (batchErrorsList.isEmpty()) {
            insert batchErrorsList;
        }

        for (DSI__c dsi : scope) {
            if (dsi.MCSBatchStatus__c == 'Pending') {
                dsi.MCSBatchStatus__c = 'Processed';
            }
        }

        update scope;
    }

    public void updateErrorMap(
        String recordId,
        Map<Id, FF_Integration_Log__c> errorMap,
        String errorLabel,
        Database.Error[] errorArr
    ) {
        String errorMsg = errorLabel;

        for (Database.Error err : errorArr) {
            errorMsg += err.getFields() + '-' + err.getMessage();
        }
        if (errorMap.containsKey(recordId)) {
            FF_Integration_Log__c errLog = errorMap.get(recordId);
            errLog.Message__c += '\n' + errorMsg;
        } else {
            FF_Integration_Log__c errLog = new FF_Integration_Log__c();
            errLog.Reference_Id__c = recordId;
            errLog.Message__c = errorMsg;
            errLog.Type__c = 'Error';
            errorMap.put(recordId, errLog);
        }
    }

    public void finish(Database.BatchableContext BC) {
        if (!quotesToInsert.isEmpty()) {
            System.debug('calling quote job');
            System.enqueueJob(new MCSDependencyQueueable(JSON.serialize(quotesToInsert), SBQQ__Quote__c.getSObjectType()));
        }
    }
}