/*
 * MCSRestRequest
 * 
 * DESCRIPTION : Called by Internally by Create<PERSON>SDSI.apxc to perform DML Actions before KeyGen HTTP Callout
 * Creates a DSI, Subscriptions 
 * 
 * Test Class:CreateMCSDSITest
 * ---------------------------------------------------------------------------------------------------------------------------------------------------
 * DEVELOPER                     DATE                                   REASON
 *---------------------------------------------------------------------------------------------------------------------------------------------------
 * Mahipal Jat		             13/05/2025                       		- Original Version - 557760 - SF/CPQ - MCS - Auto Opp Creation
 * --------------------------------------------------------------------------------------------------------------------------------------------------- 
 */

@RestResource (UrlMapping='/sfdcInternalMCS/*')

global class MCSRestRequest {
    global class WSException extends Exception {
    }

    @HttpPost
    global static CreateMCSDSI.ResponseWrapper createDSI () {
        Boolean hasError = false;
        String sErrorMsg = '';
        DSI__c dsi;
        CreateMCSDSI.ResponseWrapper Resp;
        try {

            RestRequest req = RestContext.request;
            Blob body = req.requestBody;
            String requestString = body.toString();
            System.debug(requestString);

            CreateMCSDSI.RequestWrapper item = (CreateMCSDSI.RequestWrapper) JSON.deserialize(requestString, CreateMCSDSI.RequestWrapper.class);
            System.debug(item);
            dsi = generateDSI(item);
        } catch (Exception ex) {
            //sErrorMsg =  'DSI was not generated '+ex.getMessage();
            throw new WSException('Something went Wrong: ' + ex.getMessage());
        }
        Resp = new CreateMCSDSI.ResponseWrapper (dsi, sErrorMsg);
        return Resp;
    }

    //inserting DSI
    private static DSI__c generateDSI (CreateMCSDSI.RequestWrapper request) {

        Boolean hasError = false;
        Savepoint sp = Database.setSavepoint();
        DSI__c d;
        MSTR_Placeholder_Values__c CS;
        List<Contact> contactList = new List<Contact>();

        if (request != null && request.user != null) {
            contactList = [SELECT Id, AccountId FROM Contact WHERE Email = :request.user.email];
        }

        String accountId = contactList.isEmpty() ? TriggersHelper.unmappedAccountId : contactList[0].AccountId;
        MSTR_Global_Configuation__mdt productCode = [SELECT Value__c, Type__c FROM MSTR_Global_Configuation__mdt WHERE DeveloperName = :request.productPackage LIMIT 1];
        List<String> productCodeList = productCode.Value__c.split(';');

        try {
            d = new DSI__c(Name = 'tempName', Description_Name__c = request.environmentId, Description__c = 'Environment via MCS', Version__c = request.mstrVersion, Account__c = accountId,
                RecordTypeId = Schema.SObjectType.DSI__c.getRecordTypeInfosByName().get('Archived').getRecordTypeId(), Platform__c = 'MCS',
                MCSEnvironmentName__c = request.environmentId, Status__c = 'Archived', MCSEnvironmentUrl__c = request.environmentUrl);
            if (request.subscription != null) {
                d.MCSActivationDateTime__c = request.subscription.activationDate;
                d.MCSExpirationDatetime__c = request.subscription.expirationDate;
            }
            if(contactList != null && contactList.size() > 0 && contactList[0].Id != null) {
                d.MCSEnvironmentOwner__c = contactList[0].Id;
            }
            d.Status__c = 'Trial';
            d.Support_Level__c = 'Cloud mcs';
            d.MCSBatchStatus__c = 'Pending';
            System.debug('d -> ' + d); 
            
            insert d;

            List<SBQQ__Subscription__c> lSubs = new List<SBQQ__Subscription__c>();
            Map<String, Id> mSKUs = new Map<String, Id>();
            
            if (!productCodeList.isEmpty()) {
                for (Product2 p : [SELECT Id, ProductCode FROM Product2 WHERE ProductCode IN :productCodeList AND IsActive = TRUE]) {
                    SBQQ__Subscription__c s = new SBQQ__Subscription__c(
                        SBCF_DSI__c = d.Id, 
                        SBQQ__Quantity__c =  p.productCode == '89412' ? 50 : 1,
                        SBQQ__Product__c = p.Id, 
                        Key_Group_multi__c = 'Prod Key 1', 
                        SBQQ__Account__c = d.Account__c, 
                        SBQQ__SubscriptionStartDate__c = System.today(),
                        SBQQ__ChargeType__c = 'Trial');
                    lSubs.add(s);
                }
            }
            System.debug('will be inserting lSubs: ' + lSubs);
            insert lSubs;

            List<DSI__c> lstDSI = [SELECT Id, DSI_ID__c, DSIID__c, Account__c, Account__r.Compass_Id_F__c, Version__c, Operating_System__c, Elastic_Cloud_Console__c, MCSActivationDateTime__c, MCSExpirationDatetime__c FROM DSI__c WHERE Id = :d.Id];
            d = lstDSI[0];
        } catch (Exception ex) {
            System.debug('Error: ' + ex.getMessage());
            Database.rollback(sp);
            throw new WSException('Internal Error Occurred: ' + ex.getMessage());
        }

        return d;
    }

}