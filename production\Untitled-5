Object Details:

Object Label: Integration Error Log
Object API Name: Integration_Error_Log__c
Enable Reports: True
Enable Activities: False
Deployment Status: Deployed
Fields:

Field Label	API Name	Data Type	Length/Details	Description	Indexed
Log ID	Error_Log_ID__c	Auto Number	EL-{0000000}	Unique system-generated identifier for the error log.	Yes
Timestamp	Timestamp__c	Date/Time		Exact date and time when the error occurred.	Yes
Integration Name	Integration_Name__c	Text	255	User-friendly name of the integration or external system (e.g., "SAP Order Sync", "Weather API").	Yes
Integration Point	Integration_Point__c	Text	255	Specific endpoint or operation within the integration (e.g., "Create Customer", "Fetch Product Details").	
Apex Class	Apex_Class__c	Text	255	Name of the Apex class where the error originated.	
Apex Method	Apex_Method__c	Text	255	Name of the Apex method within the class where the error occurred.	
Severity	Severity__c	Picklist	Values: Critical, Error, Warning, Info (Default: Error)	Indicates the severity of the error.	Yes
Error Code	Error_Code__c	Text	100	Specific error code from the external system or an internal error code.	
Error Message	Error_Message__c	Long Text Area	32,768	Detailed error message from the exception or the external system's response.	
Stack Trace	Stack_Trace__c	Long Text Area	131,072	Apex stack trace, if the error originated from an Apex exception.	
Request Endpoint	Request_Endpoint__c	URL	255	The complete URL of the HTTP callout.	
Request Method	Request_Method__c	Picklist	Values: GET, POST, PUT, DELETE, PATCH, HEAD, OPTIONS	HTTP method used for the callout (e.g., GET, POST).	
Request Headers	Request_Headers__c	Long Text Area	131,072	Headers sent in the HTTP request. (Consider security implications for sensitive data).	
Request Body	Request_Body__c	Long Text Area	131,072	Body of the HTTP request. (Consider security and PII; potentially mask/truncate sensitive data).	
Response Status Code	Response_Status_Code__c	Number	3, 0	HTTP status code received from the external system (e.g., 200, 404, 500).	
Response Headers	Response_Headers__c	Long Text Area	131,072	Headers received in the HTTP response.	
Response Body	Response_Body__c	Long Text Area	131,072	Body of the HTTP response. (Consider security and PII).	
Related Record ID	Related_Record_ID__c	Text	18	Salesforce Record ID (e.g., Account ID, Opportunity ID) that was being processed when the error occurred.	Yes
Related Record Object	Related_Record_Object__c	Text	255	API Name of the SObject type for the Related_Record_ID__c (e.g., "Account", "Opportunity").	
User	User__c	Lookup(User)		The Salesforce user context in which the error occurred.	
Transaction ID	Transaction_ID__c	Text	255	Salesforce transaction ID (Limits.getRequestId()), useful for correlating with Salesforce debug logs.	
Correlation ID	Correlation_ID__c	Text	255	A custom ID to correlate logs across multiple systems or transactions (e.g., an ID from the external system).	Yes
Retry Count	Retry_Count__c	Number	2, 0	If a retry mechanism is implemented, this field tracks the number of retry attempts for this callout.	
Status	Status__c	Picklist	Values: New, Open, Investigating, Resolved, Ignored, Awaiting Retry (Default: New)	Current status of this error log, for tracking and management.	Yes