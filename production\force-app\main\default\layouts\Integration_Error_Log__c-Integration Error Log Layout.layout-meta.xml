<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>Submit</excludeButtons>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Timestamp__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Integration_Name__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Integration_Point__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Severity__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Status__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Log_Type__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Transaction_Log__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Apex_Class__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Apex_Method__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Error_Code__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Related_Record_ID__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Related_Record_Object__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>User__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Transaction_ID__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Error Details</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Log_Message__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Error_Message__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Stack_Trace__c</field>
            </layoutItems>
        </layoutColumns>
        <style>OneColumn</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>HTTP Request Details</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Request_Endpoint__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Request_Method__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Request_Headers__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Request_Body__c</field>
            </layoutItems>
        </layoutColumns>
        <style>OneColumn</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>HTTP Response Details</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Response_Status_Code__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Response_Headers__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Response_Body__c</field>
            </layoutItems>
        </layoutColumns>
        <style>OneColumn</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
