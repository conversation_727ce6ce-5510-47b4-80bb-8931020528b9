/**
 * @description Queueable Apex class to persist Integration_Error_Log__c records asynchronously.
 * This helps in decoupling log insertion from the main transaction.
 */
public class LogPersisterQueueable implements Queueable {

    private List<Integration_Error_Log__c> logsToInsert;

    /**
     * @description Constructor that accepts the list of log records to be inserted.
     * @param logs A list of Integration_Error_Log__c SObjects.
     */
    public LogPersisterQueueable(List<Integration_Error_Log__c> logs) {
        this.logsToInsert = logs;
    }

    /**
     * @description Executes the queueable job, inserting the log records.
     * @param context The QueueableContext interface provides context for the job.
     */
    public void execute(QueueableContext context) {
        if (logsToInsert != null && !logsToInsert.isEmpty()) {
            try {
                // Use Database.insert with allOrNothing set to false.
                // This allows partial success if some log records fail validation for any reason,
                // and prevents the entire batch from failing if one record is problematic.
                Database.SaveResult[] saveResults = Database.insert(logsToInsert, false);

                // Optionally, iterate through SaveResults to log any individual failures during insertion.
                // This is important because the queueable job itself won't throw an error for partial success.
                for (Integer i = 0; i < saveResults.size(); i++) {
                    if (!saveResults[i].isSuccess()) {
                        Database.Error error = saveResults[i].getErrors()[0];
                        System.debug(LoggingLevel.ERROR,
                            'LogPersisterQueueable: Failed to insert an Integration_Error_Log__c record. ' +
                            'Log Message (if available): ' + logsToInsert[i].Error_Message__c + // Or a more generic message field
                            '. Status Code: ' + error.getStatusCode() +
                            '. Error Message: ' + error.getMessage() +
                            '. Fields: ' + String.join(error.getFields(), ', ')
                        );
                        // Consider a fallback mechanism if critical logs fail, e.g., a custom notification.
                    }
                }
            } catch (Exception e) {
                // Catch any unexpected exception during the DML operation.
                System.debug(LoggingLevel.ERROR,
                    'LogPersisterQueueable: A critical exception occurred during log insertion. ' +
                    'Exception Type: ' + e.getTypeName() +
                    '. Message: ' + e.getMessage() +
                    '. Stacktrace: ' + e.getStackTraceString()
                );
                // Avoid re-throwing the exception here to prevent the queueable job from
                // being re-tried indefinitely by the system if the error is persistent (e.g., bad data).
            }
        } else {
            System.debug(LoggingLevel.INFO, 'LogPersisterQueueable: execute called with no logs to insert.');
        }
    }
}