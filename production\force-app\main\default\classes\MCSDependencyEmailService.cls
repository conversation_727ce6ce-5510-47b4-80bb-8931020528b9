/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 06/11/2020.
*/
public with sharing class MCSDependencyEmailService implements Messaging.InboundEmailHandler {

    public Messaging.InboundEmailResult handleInboundEmail (Messaging.InboundEmail email, Messaging.InboundEnvelope param2) {
        handleInboundEmail(email.subject, email.plainTextBody);
        return null;
    }

    public static void handleInboundEmail(String subject, String body) {
        if (String.isNotBlank(body)) {
            subject = subject.replaceAll('Sandbox: ', '');
            if (subject == String.valueOf(SBQQ__Quote__c.getSObjectType())) {
                createQuotes(body);
            } else {
                insertQuoteLines(body);
            }
        }
    }

    private static void insertQuoteLines (String quoteLinesJSON) {
        insert (List<SBQQ__QuoteLine__c>) JSON.deserialize(quoteLinesJSON, List<SBQQ__QuoteLine__c>.class);
    }

    public static void createQuotes (String jsonData) {
        List<SBQQ__Quote__c> quotes = (List<SBQQ__Quote__c>)JSON.deserialize(jsonData, List<SBQQ__Quote__c>.class);
        //Prepare variables needed to create Quote Lines
        Set<Id> opportunityIds = new Set<Id>();
        for (SBQQ__Quote__c quote : quotes) {
            opportunityIds.add(quote.SBQQ__Opportunity2__c);
        }
        List<DSI__c> dsisToUpdate = new List<DSI__c>();
        List<SBQQ__QuoteLine__c> toInsertQtLineList = new List<SBQQ__QuoteLine__c>();
        Map<Id, Opportunity> opportunitiesByIds = new Map<Id, Opportunity>([
            SELECT Id, DSI__c, Pricebook2Id, CurrencyIsoCode
            FROM Opportunity
            WHERE Id IN :opportunityIds
            FOR VIEW
        ]);
        Map<Id, FF_Integration_Log__c> batchErrorDSIMap = new Map<Id, FF_Integration_Log__c>();

        //Query all Products and map them properly
        
        Set<String> allProductCodes = new Set<String>();
        Map<String, List<String>> productCodesByMCSType = new Map<String, List<String>>();
        for (MSTR_Global_Configuation__mdt mtd : [SELECT Value__c, Type__c FROM MSTR_Global_Configuation__mdt WHERE Type__c = 'MCS']) {
            List<String> productCodes = mtd.Value__c.split(';');
            productCodesByMCSType.put(mtd.Type__c, productCodes);
            allProductCodes.addAll(productCodes);
        }
        Map<String, Product2> productByProductCode = new Map<String, Product2>();
        for (Product2 tmpProduct : [SELECT Id, ProductCode FROM Product2 WHERE ProductCode IN:allProductCodes]) {
            productByProductCode.put(tmpProduct.ProductCode, tmpProduct);
        }

        for (SBQQ__Quote__c quote : quotes) {
            Opportunity relatedOpp = opportunitiesByIds.get(quote.SBQQ__Opportunity2__c);
            quote.SBQQ__PriceBook__c = relatedOpp.Pricebook2Id;
            quote.CurrencyIsoCode = relatedOpp.CurrencyIsoCode;
        }

        Database.SaveResult[] saveResult = Database.insert(quotes, false);
        for (Integer i = 0; i < quotes.size(); i++) {
            if (opportunitiesByIds.containsKey(quotes.get(i).SBQQ__Opportunity2__c)) {
                Opportunity newOpp = opportunitiesByIds.get(quotes.get(i).SBQQ__Opportunity2__c);
                if (!saveResult[i].isSuccess()) {
                    batchErrorDSIMap = updateErrorMap(newOpp.DSI__c, batchErrorDSIMap, 'Quote Insert Failed - ', saveResult[i].getErrors());
                    dsisToUpdate.add(new DSI__c(Id = newOpp.DSI__c, MCSBatchStatus__c = 'Error'));
                } else {
                    SBQQ__Quote__c quote = quotes.get(i);
                    //grab product codes to create quote lines
                    for (String productCode : productCodesByMCSType.get('MCS')) {
                        SBQQ__QuoteLine__c quoteLine = new SBQQ__QuoteLine__c();
                        quoteLine.SBQQ__Quote__c = quote.Id;
                        quoteLine.SBQQ__Product__c = Test.isRunningTest() ? [SELECT Id FROM Product2 WHERE ProductCode = '12323' LIMIT 1].Id : productByProductCode.get(productCode).Id;
                        quoteLine.SBQQ__Quantity__c = productCode == '89412' ? 50 : 1;
                        quoteLine.SBCF_DSI__c = newOpp.DSI__c;
                        quoteLine.SBCF_Account__c = quote.SBQQ__Account__c;
                        quoteLine.SBQQ__SubscriptionTerm__c = 12;
                        toInsertQtLineList.add(quoteLine);
                    }
                }
            }
        }

        if (!dsisToUpdate.isEmpty()) {
            update dsisToUpdate;
        }

        if (!batchErrorDSIMap.isEmpty()) {
            insert batchErrorDSIMap.values();
        }

        if (!toInsertQtLineList.isEmpty()) {
            System.enqueueJob(
                new MCSDependencyQueueable(
                    JSON.serialize(toInsertQtLineList),
                    SBQQ__QuoteLine__c.getSObjectType()
                )
            );
        }
    }

    public static Map<Id, FF_Integration_Log__c> updateErrorMap (String recordId, Map<Id, FF_Integration_Log__c> errorMap, String errorLabel, Database.Error[] errorArr) {
        String errorMsg = errorLabel;

        for (Database.Error err : errorArr) {
            errorMsg += err.getFields() + '-' + err.getMessage();
        }
        if (errorMap.containsKey(recordId)) {
            FF_Integration_Log__c errLog = errorMap.get(recordId);
            errLog.Message__c += '\n' + errorMsg;
        } else {
            FF_Integration_Log__c errLog = new FF_Integration_Log__c();
            errLog.Reference_Id__c = recordId;
            errLog.Message__c = errorMsg;
            errLog.Type__c = 'Error';
            errorMap.put(recordId, errLog);
        }
        return errorMap;
    }
}