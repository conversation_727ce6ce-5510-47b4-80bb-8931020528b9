/**
 * @description Utility class to log integration errors and debug information
 * to the Integration_Error_Log__c custom object.
 * Provides methods for immediate error logging and queued debug/info logging.
 * Declared as 'without sharing' to ensure log records can be inserted.
 */
public without sharing class IntegrationErrorLogger {

    // Static list to hold logs in memory before they are committed asynchronously
    private static List<Integration_Error_Log__c> logsToProcessQueue = new List<Integration_Error_Log__c>();

    // Default values
    private static final String DEFAULT_ERROR_SEVERITY = 'Error';
    private static final String DEFAULT_DEBUG_SEVERITY = 'Debug';
    private static final String DEFAULT_INFO_SEVERITY  = 'Info';
    private static final String DEFAULT_STATUS         = 'New';

    // Log Types (ensure these match the picklist values on Integration_Error_Log__c.Log_Type__c)
    public static final String LOG_TYPE_ERROR = 'Error';
    public static final String LOG_TYPE_DEBUG = 'Debug';
    public static final String LOG_TYPE_INFO  = 'Info';

    /**
     * @description Adds a log entry to an in-memory queue for asynchronous processing.
     * This method constructs the Integration_Error_Log__c SObject but does not insert it immediately.
     * Call commitLogsAsync() to persist all queued logs.
     * @param logType Type of log (e.g., IntegrationErrorLogger.LOG_TYPE_DEBUG).
     * @param severity Severity of the log.
     * @param integrationName Name of the integration or system component.
     * @param integrationPoint Specific operation, function, or module.
     * @param apexClassName Name of the Apex class where the log originates.
     * @param apexMethodName Name of the Apex method where the log originates.
     * @param message The primary log message.
     * @param errorCode Optional: Specific error code (more relevant for error logs).
     * @param stackTrace Optional: Apex stack trace (more relevant for error logs).
     * @param requestEndpoint Optional: HTTP callout endpoint.
     * @param requestMethod Optional: HTTP method used.
     * @param requestHeaders Optional: Headers sent in an HTTP request.
     * @param requestBody Optional: Body of an HTTP request or other payload.
     * @param responseStatusCode Optional: HTTP status code received.
     * @param responseHeaders Optional: Headers received in an HTTP response.
     * @param responseBody Optional: Body of an HTTP response or other result data.
     * @param relatedRecordId Optional: Salesforce Record ID related to this log entry.
     * @param relatedRecordObject Optional: API Name of the SObject type for relatedRecordId.
     */
    public static void queueLog(
        String logType, String severity, String integrationName, String integrationPoint,
        String apexClassName, String apexMethodName, String message,
        String errorCode, String stackTrace, String requestEndpoint, String requestMethod,
        String requestHeaders, String requestBody, Integer responseStatusCode,
        String responseHeaders, String responseBody, Id relatedRecordId, String relatedRecordObject
    ) {
        try {
            Integration_Error_Log__c logEntry = new Integration_Error_Log__c();

            logEntry.Timestamp__c = System.now();
            logEntry.Log_Type__c = String.isBlank(logType) ? LOG_TYPE_INFO : logType; // Default to Info if blank
            logEntry.Severity__c = severity;
            logEntry.Integration_Name__c = integrationName;
            logEntry.Integration_Point__c = integrationPoint;
            logEntry.Apex_Class__c = apexClassName;
            logEntry.Apex_Method__c = apexMethodName;
            logEntry.Error_Message__c = message; // Using Error_Message__c to store the main log message
            logEntry.Error_Code__c = errorCode;
            logEntry.Stack_Trace__c = stackTrace;
            logEntry.Request_Endpoint__c = requestEndpoint;
            logEntry.Request_Method__c = requestMethod;
            logEntry.Request_Headers__c = requestHeaders;
            logEntry.Request_Body__c = requestBody;
            logEntry.Response_Status_Code__c = responseStatusCode;
            logEntry.Response_Headers__c = responseHeaders;
            logEntry.Response_Body__c = responseBody;
            logEntry.Related_Record_ID__c = relatedRecordId != null ? String.valueOf(relatedRecordId) : null;
            logEntry.Related_Record_Object__c = relatedRecordObject;
            logEntry.User__c = UserInfo.getUserId();
            logEntry.Status__c = DEFAULT_STATUS;

            logsToProcessQueue.add(logEntry);

        } catch (Exception e) {
            // Catch any unexpected exception during the log SObject CREATION (not DML).
            System.debug(LoggingLevel.ERROR,
                'IntegrationErrorLogger.queueLog: Exception during log SObject creation. ' +
                'Message: ' + e.getMessage() + '. Stacktrace: ' + e.getStackTraceString()
            );
            // Avoid re-throwing to ensure the main process is not interrupted by a logging setup failure.
        }
    }

    /**
     * @description Commits all logs currently in the in-memory queue by enqueuing a LogPersisterQueueable job.
     * It's recommended to call this at the end of a transaction or a significant logical operation.
     * This method is safe to call even if there are no logs in the queue.
     */
    public static void commitLogsAsync() {
        if (!logsToProcessQueue.isEmpty()) {
            try {
                List<Integration_Error_Log__c> logsForJob = new List<Integration_Error_Log__c>(logsToProcessQueue);
                System.enqueueJob(new LogPersisterQueueable(logsForJob));
                logsToProcessQueue.clear();
            } catch (Exception e) {
                System.debug(LoggingLevel.ERROR,
                    'IntegrationErrorLogger.commitLogsAsync: Failed to enqueue LogPersisterQueueable job. ' +
                    'Error: ' + e.getMessage() + '. Logs might not have been cleared and will be re-attempted on next commit.'
                );
                // Logs remain in logsToProcessQueue and might be picked up by a subsequent commitLogsAsync call.
                // Consider a more robust fallback if this enqueue fails repeatedly (e.g., custom notification).
            }
        }
    }

    // --- New Debug Logging Methods (using the queue) ---

    /**
     * @description Queues a simple debug message.
     * @param apexClassName Name of the calling Apex class.
     * @param apexMethodName Name of the calling Apex method.
     * @param message The debug message.
     */
    public static void logDebug(String apexClassName, String apexMethodName, String message) {
        queueLog(
            LOG_TYPE_DEBUG, DEFAULT_DEBUG_SEVERITY, null, null, apexClassName, apexMethodName, message,
            null, null, null, null, null, null, null, null, null, null, null
        );
    }

    /**
     * @description Queues a debug message with integration context.
     * @param integrationName Name of the integration or system.
     * @param integrationPoint Specific operation or module.
     * @param apexClassName Name of the calling Apex class.
     * @param apexMethodName Name of the calling Apex method.
     * @param message The debug message.
     */
    public static void logDebug(String integrationName, String integrationPoint, String apexClassName, String apexMethodName, String message) {
        queueLog(
            LOG_TYPE_DEBUG, DEFAULT_DEBUG_SEVERITY, integrationName, integrationPoint, apexClassName, apexMethodName, message,
            null, null, null, null, null, null, null, null, null, null, null
        );
    }

    /**
     * @description Queues a debug message with payload and related record information.
     * @param apexClassName Name of the calling Apex class.
     * @param apexMethodName Name of the calling Apex method.
     * @param message The debug message.
     * @param requestBody Optional: Request payload or relevant data.
     * @param responseBody Optional: Response payload or relevant data.
     * @param relatedRecordId Optional: Related Salesforce Record ID.
     */
    public static void logDebug(
        String apexClassName, String apexMethodName, String message,
        String requestBody, String responseBody, Id relatedRecordId
    ) {
        String relatedObjType = relatedRecordId != null ? String.valueOf(relatedRecordId.getSObjectType()) : null;
        queueLog(
            LOG_TYPE_DEBUG, DEFAULT_DEBUG_SEVERITY, null, null, apexClassName, apexMethodName, message,
            null, null, null, null, null, requestBody, null, null, responseBody, relatedRecordId, relatedObjType
        );
    }

    // --- Existing Error Logging Methods (Updated to set Log_Type__c, still inserts synchronously by default) ---
    // You can choose to change these to also use queueLog() if desired for fully async error logging.

    /**
     * @description The most comprehensive method to log an error with detailed information.
     * Inserts the log record synchronously.
     * (Parameters as in your original method)
     */
    public static void logError(
        String integrationName, String integrationPoint, String apexClassName, String apexMethodName,
        String severity, String errorCode, String errorMessage, String stackTrace,
        String requestEndpoint, String requestMethod, String requestHeaders, String requestBody,
        Integer responseStatusCode, String responseHeaders, String responseBody,
        Id relatedRecordId, String relatedRecordObject
    ) {
        try {
            Integration_Error_Log__c errorLog = new Integration_Error_Log__c();

            errorLog.Log_Type__c = LOG_TYPE_ERROR; // Set the new field
            errorLog.Timestamp__c = System.now();
            errorLog.Integration_Name__c = integrationName;
            errorLog.Integration_Point__c = integrationPoint;
            errorLog.Apex_Class__c = apexClassName;
            errorLog.Apex_Method__c = apexMethodName;
            errorLog.Severity__c = String.isBlank(severity) ? DEFAULT_ERROR_SEVERITY : severity;
            errorLog.Error_Code__c = errorCode;
            errorLog.Error_Message__c = errorMessage;
            errorLog.Stack_Trace__c = stackTrace;
            errorLog.Request_Endpoint__c = requestEndpoint;
            errorLog.Request_Method__c = requestMethod;
            errorLog.Request_Headers__c = requestHeaders;
            errorLog.Request_Body__c = requestBody;
            errorLog.Response_Status_Code__c = responseStatusCode;
            errorLog.Response_Headers__c = responseHeaders;
            errorLog.Response_Body__c = responseBody;
            errorLog.Related_Record_ID__c = relatedRecordId != null ? String.valueOf(relatedRecordId) : null;
            errorLog.Related_Record_Object__c = relatedRecordObject;
            errorLog.User__c = UserInfo.getUserId();
            errorLog.Status__c = DEFAULT_STATUS;

            Database.SaveResult sr = Database.insert(errorLog, false);

            if (!sr.isSuccess()) {
                System.debug(LoggingLevel.ERROR,
                    'IntegrationErrorLogger.logError (sync): Failed to insert Integration_Error_Log__c. ' +
                    'Error: ' + sr.getErrors()[0].getMessage()
                );
            }
        } catch (Exception e) {
            System.debug(LoggingLevel.ERROR,
                'IntegrationErrorLogger.logError (sync): Exception during log insertion. ' +
                'Message: ' + e.getMessage() + '. Stacktrace: ' + e.getStackTraceString()
            );
        }
    }

    /**
     * @description Logs an error based on an Exception object, integration details, and callout information.
     * (Parameters as in your original method)
     */
    public static void logError(
        Exception ex, String integrationName, String integrationPoint, String apexClassName, String apexMethodName,
        HttpRequest req, HttpResponse res, Id relatedRecordId
    ) {
        String errorMessageVal = ex != null ? ex.getMessage() : 'No exception message.';
        String stackTraceVal = ex != null ? ex.getStackTraceString() : null;
        String reqEndpoint = req != null ? req.getEndpoint() : null;
        String reqMethod = req != null ? req.getMethod() : null;
        // HttpRequest does not have a simple getHeaders() map.
        // If specific headers are needed, they must be known and retrieved individually.
        String reqHeadersVal = null; // Placeholder; adjust if you have specific headers from req to log
        String reqBodyVal = req != null ? req.getBody() : null;
        Integer resStatusCodeVal = res != null ? res.getStatusCode() : null;
        // HttpResponse does not have a simple getHeaders() map.
        String resHeadersVal = null; // Placeholder; adjust if you have specific headers from res to log
        String resBodyVal = res != null ? res.getBody() : null;
        String errorCodeVal = res != null ? String.valueOf(res.getStatusCode()) : (ex != null ? ex.getTypeName() : null);
        String relatedObjType = relatedRecordId != null ? String.valueOf(relatedRecordId.getSObjectType()) : null;

        logError(
            integrationName, integrationPoint, apexClassName, apexMethodName, DEFAULT_ERROR_SEVERITY,
            errorCodeVal, errorMessageVal, stackTraceVal, reqEndpoint, reqMethod,
            reqHeadersVal, reqBodyVal, resStatusCodeVal, resHeadersVal, resBodyVal,
            relatedRecordId, relatedObjType
        );
    }

    /**
     * @description Logs an error based primarily on an Exception object and integration context.
     * (Parameters as in your original method)
     */
    public static void logError(
        Exception ex, String integrationName, String integrationPoint,
        String apexClassName, String apexMethodName, Id relatedRecordId
    ) {
        logError(ex, integrationName, integrationPoint, apexClassName, apexMethodName, null, null, relatedRecordId);
    }

    /**
     * @description Logs a simpler error message with integration name and severity.
     * (Parameters as in your original method)
     */
    public static void logError(
        String integrationName, String errorMessage, String severity,
        String apexClassName, String apexMethodName
    ) {
        logError(
            integrationName, null, apexClassName, apexMethodName, severity,
            null, errorMessage, null, null, null,
            null, null, null, null, null,
            null, null
        );
    }

    /**
     * @description Logs an error with minimal information: integration name and an exception.
     * (Parameters as in your original method)
     */
    public static void logError(Exception ex, String integrationName, String apexClassName, String apexMethodName) {
        String errorMessageVal = ex != null ? ex.getMessage() : 'No exception message.';
        String stackTraceVal = ex != null ? ex.getStackTraceString() : null;
        String errorCodeVal = ex != null ? ex.getTypeName() : null;

        logError(
            integrationName, null, apexClassName, apexMethodName, DEFAULT_ERROR_SEVERITY,
            errorCodeVal, errorMessageVal, stackTraceVal, null, null,
            null, null, null, null, null,
            null, null
        );
    }

    /**
     * @description Logs an error related to an HTTP callout, providing request and response details.
     * (Parameters as in your original method)
     */
    public static void logError(
        String integrationName, String integrationPoint, String apexClassName, String apexMethodName,
        HttpRequest req, HttpResponse res, String errorMessage, // Can be null if response status indicates error
        Id relatedRecordId
    ) {
        String reqEndpoint = req != null ? req.getEndpoint() : null;
        String reqMethod = req != null ? req.getMethod() : null;
        String reqHeadersVal = null; // Placeholder
        String reqBodyVal = req != null ? req.getBody() : null;

        Integer resStatusCodeVal = res != null ? res.getStatusCode() : null;
        String resHeadersVal = null; // Placeholder
        String resBodyVal = res != null ? res.getBody() : null;
        String finalErrorMessage = errorMessage;

        if (String.isBlank(finalErrorMessage) && res != null && res.getStatusCode() >= 400) {
            finalErrorMessage = 'HTTP Error: ' + res.getStatusCode() + ' - ' + res.getStatus();
            if (String.isNotBlank(res.getBody())) {
                finalErrorMessage += '. Response: ' + (res.getBody().length() > 500 ? res.getBody().substring(0, 500) + '...' : res.getBody());
            }
        } else if (String.isBlank(finalErrorMessage)) {
            finalErrorMessage = 'Callout completed, but an unspecified error was logged.';
        }

        String errorCodeVal = res != null ? String.valueOf(res.getStatusCode()) : null;
        String relatedObjType = relatedRecordId != null ? String.valueOf(relatedRecordId.getSObjectType()) : null;

        logError(
            integrationName, integrationPoint, apexClassName, apexMethodName, DEFAULT_ERROR_SEVERITY,
            errorCodeVal, finalErrorMessage, null, // No Apex stack trace for pure HTTP errors unless an exception was also caught
            reqEndpoint, reqMethod, reqHeadersVal, reqBodyVal,
            resStatusCodeVal, resHeadersVal, resBodyVal,
            relatedRecordId, relatedObjType
        );
    }
}
