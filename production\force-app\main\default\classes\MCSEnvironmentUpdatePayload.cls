// MCSEnvironmentUpdatePayload.cls
public class MCSEnvironmentUpdatePayload {
    public SubscriptionDetails subscription;
    public Integer numberOfUsers;
    public String plan;

    public MCSEnvironmentUpdatePayload(Date contractStartDate, Date contractEndDate) {
        this.plan = 'paid';
        this.numberOfUsers = 50; 

        if (contractStartDate != null && contractEndDate != null) {
            Datetime startDt = Datetime.newInstanceGmt(contractStartDate.year(), contractStartDate.month(), contractStartDate.day(), 0, 0, 0);
            Datetime endDt = Datetime.newInstanceGmt(contractEndDate.year(), contractEndDate.month(), contractEndDate.day(), 0, 0, 0);
            
            this.subscription = new SubscriptionDetails(
                startDt.formatGmt('yyyy-MM-dd\'T\'HH:mm:ss\'Z\''),
                endDt.formatGmt('yyyy-MM-dd\'T\'HH:mm:ss\'Z\'')
            );
        }
    }

    public class SubscriptionDetails {
        public String activationDate;
        public String expirationDate;

        public SubscriptionDetails(String activationDate, String expirationDate) {
            this.activationDate = activationDate;
            this.expirationDate = expirationDate;
        }
    }
}